export enum IMcpStatusType {
  active = 'active',
  inactive = 'inactive',
  published = 'published',
}

export const McpStatusCnMap = {
  [IMcpStatusType.active]: '已激活',
  [IMcpStatusType.inactive]: '已下线',
  [IMcpStatusType.published]: '已发布',
};

export const McpStatusTagColorMap = {
  [IMcpStatusType.active]: 'blue',
  [IMcpStatusType.inactive]: 'red',
  [IMcpStatusType.published]: 'green',
};

export enum IAccessScopType {
  private = 'private',
  public = 'public',
}

export const AccessScopeCnMap = {
  [IAccessScopType.private]: '私有',
  [IAccessScopType.public]: '公开',
};

export const AccessScopeTagColorMap = {
  [IAccessScopType.private]: 'geekblue',
  [IAccessScopType.public]: 'purple',
};

export const ParamsTypeMap = {
  string: 'string',
  number: 'number',
};

export const typeMap = {
  string: {},
  boolean: {},
  float: {},
  integer: {},
  datetime: {},
};

export const protocolTypeMap = {
  mcp_sse: 'mcp_sse',
  http: 'http',
};

export enum IMcpTypeMap {
  internal = 'internal',
}

export const McpTypeCnMap = {
  [IMcpTypeMap.internal]: '对内',
};
