// import { request } from "./utils/request";

//  const getList  = ()=> {
//     return request({
//         url: '/api/v1/admin/mcp/query',
//     });
//  }

//  export const McpApi = {
//     getList
//  };

import { request } from "@umijs/max";

async function queryMcps(data: any) {
  return request("/api/v1/admin/mcp/query", {
    method: "POST",
    data,
  });
}

interface IMcp {
}

function createMcp(data: IMcp) {
  return request("/api/v1/admin/mcp/create", {
    method: "POST",
    data,
  });
}

function updateMcp(data: IMcp) {
  return request("/api/v1/admin/mcp/update", {
    method: "POST",
    data,
  });
}

function updateMcpStatus(data: { id: number; status: string }) {
  return request("/api/v1/admin/mcp/updateStatus", {
    method: "POST",
    data,
  });
}

function queryRegisterInstances(data?: any) {
  return request("/api/v1/admin/mcp/queryRegisterInstances", {
    method: "POST",
    data,
  });
}

export function uploadMcpFile(file: any) {
  const formData = new FormData();
  formData.append("file", file);
  return request("/api/content/pool/tool/file/upload2", {
    method: "POST",
    data: formData,
  });
}

export const McpApi = {
  queryMcps,
  createMcp,
  updateMcp,
  updateMcpStatus,
  queryRegisterInstances,
  uploadMcpFile,
};
