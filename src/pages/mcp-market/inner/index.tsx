import { SearchOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Col, Empty, Input, Pagination, Row, Spin, Typography } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { defaultTenant } from '@/constants';
import { IMcpStatusType } from '@/constants/mcp';
import { McpApi } from '@/services/mcp';
import ServiceCard from '../component/service-card';

const PAGE_SIZE = 24;
const { Title } = Typography;

const McpMarketInner = () => {
  const [pageInfo, setPageInfo] = useState({
    page: 1,
    size: PAGE_SIZE,
  });

  const [data, setData] = useState<any>();
  const [keyword, setKeyword] = useState<string>();

  const { run, loading } = useRequest(
    async (params?) => {
      const res = await McpApi.queryMcps({
        ...pageInfo,
        tenant: defaultTenant,
        type: 'internal',
        status: IMcpStatusType.published,
        name: keyword,
        ...(params || {}),
      });
      return res;
    },
    {
      manual: true,
      onSuccess(data) {
        setData(data);
      },
    },
  );

  useEffect(() => {
    run();
  }, []);

  const onSearch = async (val?: string) => {
    setKeyword(val);

    // const res = await McpApi.queryMcps({
    //     name: val,
    //     type: 'internal',
    //     ...pageInfo,
    // });

    run({
      name: val,
    });
  };

  const onPageChange = (page: number, pageSize: number) => {
    setPageInfo({
      page,
      size: pageSize,
    });
    run({
      page,
      size: pageSize,
    });
  };

  return (
    <Container>
      <HeaderSection>
        <Title
          level={3}
          style={{ margin: 0, color: '#262626', fontSize: '20px' }}
        >
          MCP 服务市场
        </Title>
        <SearchContainer>
          <StyledSearch
            placeholder="搜索你感兴趣的MCP服务"
            onSearch={onSearch}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
            size="large"
            allowClear
          />
        </SearchContainer>
      </HeaderSection>

      <ContentSection>
        <Spin spinning={loading}>
          {data?.content?.length > 0 ? (
            <StyledRow gutter={[24, 24]}>
              {data?.content?.map((item: any) => (
                <Col key={item.id}>
                  <ServiceCard data={item} />
                </Col>
              ))}
            </StyledRow>
          ) : (
            !loading && (
              <EmptyContainer>
                <Empty
                  description="暂无MCP服务"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              </EmptyContainer>
            )
          )}
        </Spin>
      </ContentSection>

      {data?.totalElements > 0 && (
        <PaginationContainer>
          <Pagination
            total={data?.totalElements}
            current={pageInfo.page}
            pageSize={pageInfo.size}
            showTotal={(total) => `共 ${total} 条`}
            onChange={onPageChange}
            showSizeChanger={false}
            showQuickJumper
          />
        </PaginationContainer>
      )}
    </Container>
  );
};

const Container = styled.div`
    height: calc(100vh - 64px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
`;

const HeaderSection = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: white;
    border-radius: 10px;
    padding: 20px 26px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    flex-shrink: 0;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 13px;
        align-items: flex-start;
    }
`;

const SearchContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 16px;
`;

const StyledSearch = styled(Input.Search)`
    width: 320px;

    .ant-input {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.3s;
        font-size: 13px;

        &:hover {
            border-color: #40a9ff;
        }

        &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    }

    .ant-btn {
        border-radius: 0 6px 6px 0;
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border-color: #1890ff;

        &:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            border-color: #40a9ff;
        }
    }

    @media (max-width: 768px) {
        width: 100%;
    }
`;

const ContentSection = styled.div`
    background: white;
    border-radius: 12px;
    padding: 24px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    flex: 1;
    overflow-y: auto;
    margin-bottom: 24px;
    min-height: 0;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
`;

const StyledRow = styled(Row)`
    margin: 0 !important;

    .ant-col {
        margin-bottom: 0 !important;
        display: flex;

        > * {
            width: 100%;
        }
    }
`;

const EmptyContainer = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
`;

const PaginationContainer = styled.div`
    display: flex;
    justify-content: center;
    padding: 12px 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    flex-shrink: 0;
`;

export default McpMarketInner;
