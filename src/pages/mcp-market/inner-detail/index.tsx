import { useLocation } from '@umijs/max';
import styled from 'styled-components';
import HeaderCard from '@/pages/mcp-market/inner-detail/components/header-card';
import RightSideInfo from '@/pages/mcp-market/inner-detail/components/right-side-info';
import TabInfo from '@/pages/mcp-market/inner-detail/components/tab-info';

const McpMarketInnerDetail = () => {
  const { state } = useLocation() as { state: { data: Record<string, any> } };
  const data = state?.data || {};

  return (
    <Container>
      <HeaderCard data={data} />

      <div style={{ display: 'flex', gap: '20px' }}>
        <TabInfo data={data} />
        <RightSideInfo data={data} />
      </div>
    </Container>
  );
};

export default McpMarketInnerDetail;

const Container = styled.div`
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
`;
