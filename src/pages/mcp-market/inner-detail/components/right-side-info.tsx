import { Card, Descriptions, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import type { ReactNode } from 'react';
import styled from 'styled-components';
import { McpStatusCnMap, McpStatusTagColorMap } from '@/constants/mcp';

// 标签颜色映射
const TAG_COLORS = ['blue', 'green', 'gold', 'purple', 'cyan'];

interface IProps {
  data: Record<string, any>;
}

interface DescriptionItemType {
  key: string;
  label: string;
  render: (data: Record<string, any>) => ReactNode;
}

const RightSideInfo = ({ data }: IProps) => {
  const configInfo = JSON.stringify(
    {
      endpoint: {
        url:
          data.requestHost && data.sseEndpoint
            ? `${data.requestHost}${data.sseEndpoint}`
            : '-',
      },
    },
    null,
    2,
  );

  const getTagColor = (index: number) => {
    return TAG_COLORS[index % TAG_COLORS.length];
  };

  // 描述项配置
  const descriptionItems: DescriptionItemType[] = [
    {
      key: 'version',
      label: '版本',
      render: (data) =>
        data.extInfo?.version ? `v${data.extInfo?.version}` : '-',
    },
    {
      key: 'accessScope',
      label: '可见范围',
      render: (data) => (
        <Tag color={data.accessScope === 'public' ? 'success' : 'warning'}>
          {data.accessScope === 'public' ? '公开' : '私有'}
        </Tag>
      ),
    },
    {
      key: 'status',
      label: '状态',
      render: (data) => (
        <Tag
          color={
            McpStatusTagColorMap[
              data.status as keyof typeof McpStatusTagColorMap
            ]
          }
        >
          {McpStatusCnMap[data.status as keyof typeof McpStatusCnMap]}
        </Tag>
      ),
    },
    {
      key: 'createdAt',
      label: '创建时间',
      render: (data) =>
        data.createdAt ? dayjs(data.createdAt).format('YYYY-MM-DD') : '-',
    },
    {
      key: 'updatedAt',
      label: '更新时间',
      render: (data) =>
        data.updatedAt ? dayjs(data.updatedAt).format('YYYY-MM-DD') : '-',
    },
    {
      key: 'maintainer',
      label: '维护方',
      render: (data) => data.extInfo?.maintainer || data.appName || '-',
    },
    {
      key: 'serviceUrl',
      label: '服务官方 URL',
      render: (data) => (
        <a
          href={data.extInfo?.serviceUrl || '#'}
          target="_blank"
          rel="noopener noreferrer"
        >
          {data.extInfo?.serviceUrl || data.requestHost || '-'}
        </a>
      ),
    },
    {
      key: 'tags',
      label: '标签',
      render: (data) => {
        const tagArray = data.tags
          ? data.tags.split(',').map((tag: string) => tag.trim())
          : [];
        return (
          <Space wrap>
            {tagArray.map((tag: string, index: number) => (
              <Tag key={tag} color={getTagColor(index)}>
                {tag}
              </Tag>
            ))}
          </Space>
        );
      },
    },
  ];

  return (
    <Container>
      <StyledCard
        title="MCP Server 配置信息"
        // extra={<Button type="text" icon={<CopyOutlined />} onClick={handleCopy}>复制</Button>}
      >
        <CodeBlock>
          <code>{configInfo}</code>
        </CodeBlock>
      </StyledCard>

      <Card title="服务信息">
        <Descriptions column={1} bordered={false} size="small">
          {descriptionItems.map((item) => (
            <Descriptions.Item key={item.key} label={item.label}>
              {item.render(data)}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>
    </Container>
  );
};

const Container = styled.div`
    flex: 1;
`;

const StyledCard = styled(Card)`
    margin-bottom: 20px;
`;

const CodeBlock = styled.pre`
    background: #f6f8fa;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    max-height: 200px;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
`;

export default RightSideInfo;
