import { ApiOutlined, FileTextOutlined, ToolOutlined } from '@ant-design/icons';
import { Card, Divider, Tabs, Typography } from 'antd';
import styled from 'styled-components';

interface IProps {
  data: Record<string, any>;
}

interface ToolType {
  name: string;
  description: string;
  inputSchema: Record<string, any>;
}

const { Title, Paragraph, Text } = Typography;

const TabInfo = ({ data }: IProps) => {
  const tools = data.meta?.tools || [];

  // 构建配置信息JSON
  const configInfo = JSON.stringify(
    {
      endpoint: {
        url: data.requestHost ? `${data.requestHost}${data.sseEndpoint}` : '-',
      },
    },
    null,
    2,
  );

  // 工具列表
  const renderTools = () => {
    if (!tools || tools.length === 0) {
      return <Text type="secondary">暂无可用工具</Text>;
    }
    return tools.map((tool: ToolType, index: number) => (
      <ToolContainer key={tool.name}>
        <Title level={5}>
          {index + 1}. {tool.name} - {tool.description}
        </Title>
        <StyledList>
          <li>
            <Text strong>功能:</Text> {tool.description}
          </li>
          <li>
            <Text strong>参数:</Text>
            <CodeBlock>
              <code>{JSON.stringify(tool.inputSchema, null, 2)}</code>
            </CodeBlock>
          </li>
        </StyledList>
      </ToolContainer>
    ));
  };

  // 协议信息
  const renderProtocols = () => (
    <>
      <Text>{data.name || 'MCP'} 服务支持的特殊协议：</Text>
      <ProtocolSection>
        <Title level={5}>
          {data.protocol === 'mcp_sse'
            ? 'SSE (Server-Sent Events)'
            : data.protocol}{' '}
          - 推荐
        </Title>
        <StyledList>
          <li>
            <Text strong>URL:</Text> {data.requestHost}
            {data.sseEndpoint}
          </li>
          <li>
            <Text strong>特点:</Text> MCP 官方版项目，支持
            Cloudflare，推荐用于大多数场景
          </li>
        </StyledList>
      </ProtocolSection>

      {data.messageEndpoint && (
        <ProtocolSection>
          <Title level={5}>HTTP API</Title>
          <StyledList>
            <li>
              <Text strong>URL:</Text> {data.requestHost}
              {data.messageEndpoint}
            </li>
            <li>
              <Text strong>特点:</Text> 新协议，支持 Cloudflare 和 OpenAI
            </li>
          </StyledList>
        </ProtocolSection>
      )}
    </>
  );

  return (
    <Card style={{ flex: 3 }}>
      <Tabs defaultActiveKey="info">
        <Tabs.TabPane
          tab={
            <span>
              <FileTextOutlined /> 服务详情
            </span>
          }
          key="info"
        >
          <Title level={4}>{data.name || 'MCP Server'}</Title>
          <Text type="secondary">{data.description || '暂无详细描述'}</Text>

          <Divider />
          <SectionTitle level={4}>
            <ToolOutlined /> 可用工具
          </SectionTitle>
          {renderTools()}

          <Divider />
          <SectionTitle level={4}>
            <ApiOutlined /> 支持的协议
          </SectionTitle>
          {renderProtocols()}
        </Tabs.TabPane>

        <Tabs.TabPane
          tab={
            <span>
              <ToolOutlined /> 工具
            </span>
          }
          key="tools"
        >
          <TabContent>{renderTools()}</TabContent>
        </Tabs.TabPane>

        <Tabs.TabPane
          tab={
            <span>
              <ApiOutlined /> 交互示例
            </span>
          }
          key="examples"
        >
          <TabContent>
            <Title level={4}>客户端配置示例</Title>
            <Paragraph>大多数客户端 (如 Windsurf, Cursor)</Paragraph>
            <CodeBlock>
              <code>{configInfo}</code>
            </CodeBlock>
          </TabContent>
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

// 样式化组件
const StyledList = styled.ul`
    padding-left: 20px;
`;

const ToolContainer = styled.div`
    margin-bottom: 24px;
`;

const CodeBlock = styled.pre`
    margin-top: 8px;
    background: #f6f8fa;
    padding: 8px;
    border-radius: 6px;
    overflow-x: auto;
`;

const TabContent = styled.div`
    padding: 16px;
`;

const SectionTitle = styled(Title)`
    margin-top: 20px;
`;

const ProtocolSection = styled.div`
    margin-top: 16px;
`;

export default TabInfo;
