import { LeftOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, Card, Tag, Typography } from 'antd';
import styled from 'styled-components';
import { McpStatusCnMap, McpStatusTagColorMap } from '@/constants/mcp';

interface IProps {
  data: Record<string, any>;
}

const HeaderCard = ({ data }: IProps) => {
  const tagArray = data.tags
    ? data.tags.split(',').map((tag: string) => tag.trim())
    : [];

  const handleBack = () => {
    history.back();
  };

  return (
    <StyledCard
      title={
        <Button icon={<LeftOutlined />} type="text" onClick={handleBack}>
          返回MCP列表
        </Button>
      }
    >
      <CardHeader>
        <IconWrapper>
          {data.icon ? (
            <ServiceIcon src={data.icon} alt="logo" />
          ) : (
            <DefaultIcon></DefaultIcon>
          )}
        </IconWrapper>

        <div>
          <Typography.Title level={3} style={{ margin: 0 }}>
            {data.name || '未命名服务'}
          </Typography.Title>
          <TitleWrapper>
            <Tag color={data.accessScope === 'public' ? 'success' : 'warning'}>
              {data.accessScope === 'public' ? '公开' : '私有'}
            </Tag>
            <Tag
              color={
                McpStatusTagColorMap[
                  data.status as keyof typeof McpStatusTagColorMap
                ]
              }
            >
              {McpStatusCnMap[data.status as keyof typeof McpStatusCnMap]}
            </Tag>
            {data.extInfo?.version && (
              <Typography.Text type="secondary">
                版本 v{data.extInfo?.version}
              </Typography.Text>
            )}
          </TitleWrapper>
        </div>
      </CardHeader>

      <div>
        <Description>{data.description || '暂无描述'}</Description>
      </div>

      <TagsContainer>
        {tagArray.map((tag: string, index: number) => (
          <Tag
            key={tag}
            color={
              index % 3 === 0 ? 'blue' : index % 3 === 1 ? 'cyan' : 'default'
            }
          >
            {tag}
          </Tag>
        ))}
      </TagsContainer>
    </StyledCard>
  );
};

const StyledCard = styled(Card)`
    margin-bottom: 20px;
`;

const CardHeader = styled.div`
    display: flex;
    align-items: center;
    margin-bottom: 16px;
`;

const IconWrapper = styled.div`
    margin-right: 16px;
`;

const ServiceIcon = styled.img`
    width: 48px;
    height: 48px;
    border-radius: 8px;
`;

const DefaultIcon = styled.div`
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: #f6f8fa;
`;

const TitleWrapper = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 5px;
`;

const Description = styled(Typography.Paragraph)`
    margin-top: 12px;
`;

const TagsContainer = styled.div`
    margin-top: 16px;
`;

export default HeaderCard;
