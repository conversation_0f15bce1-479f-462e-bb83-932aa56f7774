import { history } from '@@/core/history';
import { ClockCircleOutlined, ToolOutlined } from '@ant-design/icons';
import { Avatar, Card, Flex, Tag, Tooltip, Typography } from 'antd';
import { useMemo } from 'react';
import styled from 'styled-components';
import { defaultMcpCover } from '@/constants';

const { Text, Paragraph } = Typography;

interface IProps {
  data?: Record<string, any>;
}

const ServiceCard = ({ data }: IProps) => {
  const { name, description, icon, tags, updatedAt, meta } = data || {};

  const CardHeader = useMemo(() => {
    return (
      <Flex align="center" gap={12}>
        <StyledAvatar src={icon || defaultMcpCover} size={40} />
        <div style={{ flex: 1, minWidth: 0 }}>
          <Tooltip title={name}>
            <ServiceName>{name}</ServiceName>
          </Tooltip>
          <ServiceMeta>
            <Flex align="center" gap={4}>
              <ToolOutlined style={{ fontSize: 12, color: '#666' }} />
              <Text type="secondary" style={{ fontSize: 12 }}>
                {meta?.tools?.length || 0} 个工具
              </Text>
            </Flex>
          </ServiceMeta>
        </div>
      </Flex>
    );
  }, [name, icon, meta]);

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  };

  return (
    <>
      <StyledCard
        hoverable
        title={CardHeader}
        onClick={() => {
          history.push(`/mcp-market/inner/${data?.id}`, { data });
        }}
        styles={{
          body: { padding: '16px' },
          header: {
            padding: '16px',
            borderBottom: '1px solid #f0f0f0',
          },
        }}
      >
        <CardContent>
          <DescriptionSection>
            <Paragraph
              ellipsis={{ rows: 4, tooltip: description }}
              style={{
                margin: 0,
                color: '#666',
                fontSize: 12,
                lineHeight: 1.5,
              }}
            >
              {description || '暂无描述'}
            </Paragraph>
          </DescriptionSection>

          <FooterSection>
            <Flex justify="space-between" align="center">
              <FooterLeft>
                {tags && (
                  <Flex wrap gap={6}>
                    {tags
                      .split(',')
                      .slice(0, 2)
                      .map((tag: string) => (
                        <Tooltip key={tag.trim()} title={tag.trim()}>
                          <StyledTag bordered={false}>{tag.trim()}</StyledTag>
                        </Tooltip>
                      ))}
                    {tags.split(',').length > 2 && (
                      <Tooltip
                        title={
                          <div>
                            <div
                              style={{
                                marginBottom: 4,
                                fontWeight: 'bold',
                              }}
                            >
                              所有标签：
                            </div>
                            {tags.split(',').map((tag: string) => (
                              <div key={tag} style={{ marginBottom: 2 }}>
                                • {tag.trim()}
                              </div>
                            ))}
                          </div>
                        }
                      >
                        <StyledTag bordered={false}>
                          +{tags.split(',').length - 2}
                        </StyledTag>
                      </Tooltip>
                    )}
                  </Flex>
                )}
              </FooterLeft>
              <FooterRight>
                <Flex align="center" gap={4}>
                  <ClockCircleOutlined
                    style={{ fontSize: 12, color: '#999' }}
                  />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {formatDate(updatedAt)}
                  </Text>
                </Flex>
              </FooterRight>
            </Flex>
          </FooterSection>
        </CardContent>
      </StyledCard>
      {/* <RegisterModal
            type="edit"
            data={data}
            trigger={<Card
                hoverable
                style={{ width: 240, height: 140 }}>
                <Meta
                    avatar={<Avatar src={icon || defaultMcpCover} />}
                    title={name}
                    description={<Tooltip title={description}>{description}</Tooltip>} />
                <Flex style={{ marginTop: 14 }}>
                    {tags?.split(',')?.map((tag: string) => <Tag key={tag} bordered={false}>{tag}</Tag>)}
                </Flex>
            </Card>}
            drawerProps={{ title: '详情' }}
        /> */}
    </>
  );
};

const StyledCard = styled(Card)`
    width: 280px !important;
    height: 246px;
    border-radius: 11px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;

    &:hover {
        border-color: #1890ff;
        box-shadow: 0 7px 22px rgba(24, 144, 255, 0.12);
        transform: translateY(-2px);
    }

    .ant-card-head {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-bottom: 1px solid #f0f0f0;
        border-radius: 11px 11px 0 0;
        flex-shrink: 0;
        padding: 14px 18px;
    }

    .ant-card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 18px;
    }
`;

const StyledAvatar = styled(Avatar)`
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ServiceName = styled.div`
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    line-height: 1.4;
`;

const ServiceMeta = styled.div`
    margin-top: 4px;
`;

const CardContent = styled.div`
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    min-height: 0;
`;

const DescriptionSection = styled.div`
    flex: 1;
    margin-bottom: 16px;
    overflow: hidden;
    min-height: 0;
`;

const FooterSection = styled.div`
    margin-top: auto;
    padding-top: 12px;
    border-top: 1px solid #f5f5f5;
`;

const FooterLeft = styled.div`
    flex: 1;
    min-width: 0;
`;

const FooterRight = styled.div`
    flex-shrink: 0;
    margin-left: 12px;
`;

const StyledTag = styled(Tag)`
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border: 1px solid #91d5ff;
    color: #1890ff;
    border-radius: 5px;
    font-size: 10px;
    padding: 1px 5px;
    margin: 0;
    max-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    cursor: pointer;

    &:hover {
        background: linear-gradient(135deg, #bae7ff 0%, #e6f7ff 100%);
        border-color: #40a9ff;
    }
`;

export default ServiceCard;
