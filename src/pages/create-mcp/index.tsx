import { LeftOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Button } from "antd";
import { history } from "@umijs/max";
import { useRef } from "react";
import RegisterForm from "./component/register-form";

const CreateMcp = () => {
    const formRef = useRef(null);

    const onSubmit = () => {
        try {
            // @ts-ignore
            formRef.current?.onSubmit();
        } catch (error) {
            console.log('error', error);

        }
    };


    const onBack = () => {
        history.back();

    };

    return <PageContainer
        breadcrumbRender={() => false}
        title={<div>
            <LeftOutlined style={{ cursor: 'pointer' }} onClick={onBack} /> 创建MCP Server
        </div>}
        footer={[
            <Button onClick={onBack}>取消</Button>,
            <Button type="primary" onClick={onSubmit}>创建 MCP Server</Button>
        ]}
    >
        <RegisterForm ref={formRef}
            onSuccess={() => {
                history.back();

            }}
        />

    </PageContainer>
}


export default CreateMcp;