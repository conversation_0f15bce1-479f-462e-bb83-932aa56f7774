import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useParams } from '@umijs/max';
import { Button, Card, Col, Form, Input, message, Row, Select } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';
import StringTags from '@/components/StringTags';
import { defaultTenant } from '@/constants';
import {
  AccessScopeCnMap,
  IMcpStatusType,
  protocolTypeMap,
} from '@/constants/mcp';
import { McpApi } from '@/services/mcp';
import ToolCard from './tool-card';
import ImageUploader from "@/components/ImageUploader";

const gutter = 16;

interface IProps {
  type?: 'edit' | 'create';
  data?: any;
  onSuccess?: () => void;
  disabled?: boolean;
}

const RegisterForm = (props: IProps, ref: any) => {
  const { type, data, onSuccess, disabled } = props;
  const [form] = Form.useForm();

  const isEdit = type === 'edit';

  useImperativeHandle(ref, () => {
    return {
      form,
      onSubmit,
    };
  });

  const transform2ApiValue = (val: any) => {
    if (!val) return;

    const required: string[] = [];

    const properties = val?.reduce((acc: Record<string, any>, cur: any) => {
      if (cur?.required) {
        required.push(cur?.name);
      }

      acc[cur.name] = {
        type: cur.type,
        description: cur.v,
      };

      return acc;
    }, {});

    return {
      type: 'object',
      properties,
      required,
    };
  };

  const onSubmit = async () => {
    // try {
    const values = await form.validateFields();
    const { tools, ...others } = values || {};
    const newTools = tools?.map((tool: any) => {
      const { inputs, ...rest } = tool || {};
      return {
        ...rest,
        inputSchema: transform2ApiValue(inputs),
      };
    });
    const params = {
      ...(data || {}),
      ...others,
      meta: {
        tools: newTools,
      },
      type: 'internal',
      tenant: defaultTenant,
      status: isEdit ? data?.status : IMcpStatusType.active,
    };

    const service = isEdit ? McpApi.updateMcp : McpApi.createMcp;
    const res = await service(params);
    if (res.code === 200) {
      message.success(isEdit ? '编辑成功' : '创建成功');
      // history.push('/service-management/register')
      // history.back();
      onSuccess?.();
    }

    // } catch (error) {
    //     console.log('提交失败：', error)
    //     throw new Error(error);
    // }
  };

  return (
    <Form layout="vertical" form={form} disabled={disabled}>
      <Card title="基础信息">
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Item
              label="MCP Code"
              name="code"
              layout="vertical"
              rules={[{ required: true }]}
            >
              <Input disabled={disabled} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="通信协议"
              name="protocol"
              rules={[{ required: true }]}
            >
              <Select
                disabled={disabled}
                options={Object.keys(protocolTypeMap)?.map((item) => ({
                  label: item,
                  value: item,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Item
              label="访问范围"
              name="accessScope"
              rules={[{ required: true }]}
            >
              <Select
                disabled={disabled}
                options={Object.keys(AccessScopeCnMap).map((scope) => ({
                  label:
                    AccessScopeCnMap[scope as keyof typeof AccessScopeCnMap],
                  value: scope,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="sseEndpoint"
              name="sseEndpoint"
              rules={[{ required: true }]}
            >
              <Input disabled={disabled} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={12}>
            <Form.Item
              label="messageEndpoint"
              name="messageEndpoint"
              rules={[{ required: true }]}
            >
              <Input disabled={disabled} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="所属应用" name="appName">
              <Input disabled={disabled} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="工具列表" style={{ margin: '20px 0' }}>
        <Form.List
          name="tools"
          rules={[
            {
              validator: async (rule, value, callback) => {
                if (!value || value?.length < 1) {
                  return Promise.reject(new Error('至少配置一个工具'));
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field, index) => (
                <ToolCard
                  field={field}
                  key={field.key}
                  title={`工具 ${index + 1}`}
                  disabled={disabled}
                  extra={
                    fields.length && !isEdit ? (
                      <CloseOutlined onClick={() => remove(field.name)} />
                    ) : null
                  }
                  type={type}
                />
              ))}
              <Form.Item>
                {!isEdit && (
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    style={{ width: '100%' }}
                    onClick={() => add()}
                  >
                    添加配置
                  </Button>
                )}

                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
      </Card>

      <Card title="发布信息">
        <Row gutter={gutter}>
          <Col span={24}>
            <Form.Item label="MCP 名称" name="name">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="标签" name="tags">
              <StringTags />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={gutter}>
          <Col span={24}>
            <Form.Item label="服务 Logo Url" name="icon">
              <ImageUploader
                beforeUpload={async (file: any) => {
                  const res = await McpApi.uploadMcpFile(file);
                  console.log("上传结果:", res);
                  return false;
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="服务描述" name="description">
              <Input.TextArea />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </Form>
  );
};

export default forwardRef(RegisterForm);
