import { CloseOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Flex,
  Form,
  Input,
  Row,
  Select,
  Switch,
} from 'antd';
import { typeMap } from '@/constants/mcp';

const gutter = 16;

// @ts-ignore
const ToolCard = (props) => {
  const { title, extra, field, type, disabled } = props;

  const isEdit = type === 'edit';

  return (
    <Card title={title} extra={extra} size="small">
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.Item
            label="工具名称"
            name={[field.name, 'name']}
            rules={[{ required: true }]}
          >
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="uri"
            name={[field.name, 'path']}
            rules={[{ required: true }]}
          >
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={gutter}>
        <Col span={12}>
          <Form.Item
            label="方法"
            name={[field.name, 'method']}
            rules={[{ required: true }]}
          >
            <Select
              disabled={disabled}
              options={[
                { label: 'GET', value: 'GET' },
                { label: 'POST', value: 'POST' },
              ]}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="描述"
            name={[field.name, 'description']}
            rules={[{ required: true }]}
          >
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
      </Row>

      <Form.List name={[field.name, 'inputs']}>
        {(fields, { add, remove }, { errors }) => (
          <>
            {fields.map((field, index) => {
              return (
                <Form.Item
                  label={index === 0 ? '工具参数' : ''}
                  required={false}
                  key={field.key}
                  wrapperCol={{ span: 24 }}
                >
                  <Flex style={{ width: '100%' }} gap={4}>
                    <Form.Item
                      name={[field.name, 'name']}
                      // label="参数名称"
                      // labelCol={{ span: 4 }}
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: '请输入参数名称',
                        },
                      ]}
                    >
                      <Input
                        disabled={disabled}
                        placeholder="参数名称"
                        size="small"
                      />
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'type']}
                      // label="参数类型"
                      // labelCol={{ span: 4 }}
                      rules={[
                        {
                          required: true,
                          message: '请选择参数类型',
                        },
                      ]}
                    >
                      <Select
                        placeholder="参数类型"
                        size="small"
                        disabled={disabled}
                        options={Object.keys(typeMap)?.map((key) => ({
                          label: key,
                          value: key,
                        }))}
                      />
                    </Form.Item>
                    <Form.Item
                      // label="是否必填"
                      name={[field.name, 'required']}
                      // labelCol={{ span: 4 }}
                    >
                      <Switch
                        disabled={disabled}
                        size="small"
                        checkedChildren="必填"
                        unCheckedChildren="非必填"
                      />
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'description']}
                      // label="参数描述"
                      // labelCol={{ span: 4 }}
                      style={{ flex: 1 }}
                    >
                      <Input
                        disabled={disabled}
                        placeholder="参数描述"
                        size="small"
                      />
                    </Form.Item>
                    {!isEdit && (
                      <Form.Item>
                        <DeleteOutlined
                          onClick={() => remove(field.name)}
                          style={{ color: '#ff4d4f' }}
                        />
                      </Form.Item>
                    )}
                  </Flex>
                </Form.Item>
              );
            })}
            {!isEdit && (
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  style={{ width: '100%' }}
                  icon={<PlusOutlined />}
                >
                  添加工具参数
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );
};

export default ToolCard;
