import { Divider, Space, Table, Tooltip } from 'antd';

interface IProps {
  value: {
    tools: any[];
  };
  onChange?: (val: any) => void;
  isPreview?: boolean;
}

const MetaToolsPreview = (props: IProps) => {
  const { value } = props;

  // const onToolsChange = (index: number, val: any) => {
  //     const newTools = value?.tools?.map((item, idx) => {
  //         if (idx === index) return val;
  //         return item;
  //     });
  //
  //     onChange?.({
  //         ...(value || {}),
  //         tools: newTools
  //     });
  // };

  // const onDelete = (index: number) => {
  //     const newTools = value?.tools?.filter((item, idx) => {
  //         if (idx === index) return false;
  //         return true;
  //     });
  //
  //     onChange?.({
  //         ...(value || {}),
  //         tools: newTools
  //     });
  // }

  return (
    <Table
      size="small"
      columns={[
        { title: '名称', dataIndex: 'name' },
        { title: '描述', dataIndex: 'description' },
        {
          title: '输入参数',
          dataIndex: 'inputSchema',
          render(value) {
            return (
              <Space split={<Divider type="vertical" />}>
                {Object.keys(value?.properties || {})?.map((item) => {
                  const cur = value?.properties?.[item];
                  return (
                    <Tooltip
                      key={item}
                      title={
                        <div>
                          <div>【参数】：{item}</div>
                          <div>【类型】：{cur?.type}</div>
                          {cur?.description && (
                            <div>【描述】：{cur?.description}</div>
                          )}
                        </div>
                      }
                    >
                      <a>{item}</a>
                    </Tooltip>
                  );
                })}
              </Space>
            );
          },
        },
        { title: 'uri', dataIndex: 'path' },
        { title: '方法', dataIndex: 'method' },
        // {
        //     title: '操作', dataIndex: 'operate', render(value, record, index) {
        //         if (isPreview) {
        //             return <MetaModal
        //                 value={record}
        //                 trigger={<Button type="link">详情</Button>}
        //                 onChange={val => onToolsChange(index, val)}
        //                 isPreview
        //             />
        //         }
        //         return <Space>
        //             <MetaModal
        //                 value={record}
        //                 trigger={<Button type="link">编辑</Button>}
        //                 onChange={val => onToolsChange(index, val)}
        //             />
        //             <Popconfirm title="确认删除？" onConfirm={() => onDelete(index)}>
        //                 <Button type="text" danger>删除</Button>
        //             </Popconfirm>
        //         </Space>
        //     },
        // }
      ]}
      dataSource={value?.tools}
      pagination={false}
    />
  );
};

export default MetaToolsPreview;
