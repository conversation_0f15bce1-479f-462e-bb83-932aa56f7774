import { <PERSON><PERSON>, <PERSON>, Drawer, Flex, Form, Input, Modal, Select, Space, Switch } from "antd";
import { useState } from "react";
// import InputParams from "./input-params";
import { CloseOutlined, MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { typeMap } from "@/constants/mcp";

interface IProps {
    trigger: React.ReactNode;
    value?: {
        inputSchema: {
            properties: Record<string, any>;
            required: string[];
            type: string;
        }
    };
    onChange: (val: any) => void;
    isPreview?: boolean;
};

const formItemLayout = {
    labelCol: {
        // xs: { span: 24 },
        // sm: { span: 4 },
        span: 4
    },
    wrapperCol: {
        // xs: { span: 24 },
        // sm: { span: 20 },
    },
};

const formItemLayoutWithOutLabel = {
    wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 20, offset: 4 },
    },
};


const MetaModal = (props: IProps) => {
    const { trigger, value, onChange, isPreview } = props;
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const transform2FormValue = (val: IProps['value']['inputSchema']) => {
        if (!val?.properties) return;

        const inputs = Object.keys(val?.properties).map(item => {
            const cur = val?.properties?.[item];
            return {
                name: item,
                description: cur.description,
                type: cur.type,
                required: val?.required?.includes(item)
            }
        })

        return inputs;
    };

    const transform2ApiValue = (val) => {
        if (!val) return;

        const required: string[] = [];

        const properties = val?.reduce((acc, cur) => {
            if (cur?.required) {
                required.push(cur?.name);
            }

            acc[cur.name] = {
                type: cur.type,
                description: cur.v
            };

            return acc


        }, {});

        return {
            type: "object",
            properties,
            required,
        }
    }

    const onOpen = () => {
        const { inputSchema, ...rest } = value || {};
        const inputs = transform2FormValue(inputSchema);
        form.setFieldsValue({
            ...rest,
            inputs,
        });
        setOpen(true);
    };

    const onCancel = () => {



        setOpen(false);
    };

    const onOk = async () => {
        const values = await form.validateFields();
        const { inputs, ...rest } = values;
        const inputSchema = transform2ApiValue(values?.inputs);

        onChange?.({
            inputSchema,
            ...rest
        })
        setOpen(false);
    };

    return <>
        <Flex onClick={onOpen}>
            {trigger || <Button>创建</Button>}
        </Flex>
        <Drawer
            title={isPreview ? '查看' : "创建"}
            width={800}
            open={open}
            onClose={onCancel}
            footer={isPreview ? null : <Flex justify="end">
                <Space>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type="primary" onClick={onOk}>提交</Button>
                </Space>
            </Flex>}
        >
            <Form labelCol={{ span: 4 }} form={form}>
                <Form.Item label="工具名称" name="name" rules={[{ required: true }]}>
                    <Input disabled={isPreview} />
                </Form.Item>
                <Form.Item label="uri" name="path" rules={[{ required: true }]}>
                    <Input disabled={isPreview} />
                </Form.Item>
                <Form.Item label="方法" name="method" rules={[{ required: true }]}>
                    <Select
                        disabled={isPreview}
                        options={[
                            { label: 'GET', value: 'GET' },
                            { label: 'POST', value: 'POST' },
                        ]} />
                </Form.Item>
                <Form.Item label="描述" name="description" rules={[{ required: true }]}>
                    <Input.TextArea rows={4} disabled={isPreview} />
                </Form.Item>
                <Form.List
                    name="inputs"
                // rules={[
                //     {
                //         validator: async (_, names) => {
                //             if (!names || names.length < 2) {
                //                 return Promise.reject(new Error('At least 2 passengers'));
                //             }
                //         },
                //     },
                // ]}
                >
                    {(fields, { add, remove }, { errors }) => (
                        <>
                            {fields.map((field, index) => {
                                return (
                                    <Form.Item
                                        {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                                        label={index === 0 ? '输入参数' : ''}
                                        required={false}
                                        key={field.key}>
                                        <Card title={`参数 ${index + 1}`} size="small"
                                            extra={fields.length && !isPreview ? (
                                                <CloseOutlined
                                                    onClick={() => remove(field.name)}
                                                />
                                            ) : null}>
                                            <Form.Item
                                                name={[field.name, 'name']}
                                                label="参数名称"
                                                labelCol={{ span: 4 }}
                                                rules={[
                                                    {
                                                        required: true,
                                                        whitespace: true,
                                                        message: "请输入参数名称",
                                                    },
                                                ]}
                                            ><Input disabled={isPreview} />
                                            </Form.Item>
                                            <Form.Item
                                                name={[field.name, 'type']}
                                                label="参数类型"
                                                labelCol={{ span: 4 }}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: "请选择参数类型",
                                                    },
                                                ]}>
                                                <Select disabled={isPreview}
                                                    options={Object.keys(typeMap)?.map(key => ({ label: key, value: key }))} />
                                            </Form.Item>
                                            <Form.Item
                                                label="是否必填"
                                                name={[field.name, 'required']}
                                                labelCol={{ span: 4 }}
                                            >
                                                <Switch disabled={isPreview}
                                                    checkedChildren="必填" unCheckedChildren="非必填" />
                                            </Form.Item>
                                            <Form.Item
                                                name={[field.name, 'description']}
                                                label="参数描述"
                                                labelCol={{ span: 4 }}
                                            >
                                                <Input.TextArea disabled={isPreview} />
                                            </Form.Item>
                                        </Card>
                                        {false && <Form.Item
                                            {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                                            label={index === 0 ? 'Passengers' : ''}
                                            required={false}
                                            key={field.key}
                                        >
                                            <Form.Item
                                                {...field}
                                                validateTrigger={['onChange', 'onBlur']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        whitespace: true,
                                                        message: "Please input passenger's name or delete this field.",
                                                    },
                                                ]}
                                                noStyle
                                            >
                                                <Input placeholder="passenger name" style={{ width: '60%' }} />
                                            </Form.Item>
                                        </Form.Item>}
                                    </Form.Item>
                                )
                            })}
                            {!isPreview && <Form.Item {...formItemLayoutWithOutLabel}>
                                <Button
                                    type="dashed"
                                    onClick={() => add()}
                                    style={{ width: '100%' }}
                                    icon={<PlusOutlined />}
                                >
                                    添加输入参数
                                </Button>
                                <Form.ErrorList errors={errors} />
                            </Form.Item>}
                        </>
                    )}
                </Form.List>
            </Form>
        </Drawer>
    </>
};

export default MetaModal;