import { AccessScopeCnMap, IMcpStatusType, protocolTypeMap } from "@/constants/mcp";
import { McpApi } from "@/services/mcp";
import { <PERSON><PERSON>, Drawer, Flex, Form, Input, message, Select, Space } from "antd";
import { useState } from "react";
import MetaModal from "./meta-modal";
import MetaToolsPreview from "./meta-tools-preview";
import { defaultTenant } from "@/constants";

interface IProps {
    trigger: React.ReactNode;
    data?: Record<string, any>;
    onChange?: (val?: any) => void;
    type?: 'edit' | 'create';
    drawerProps?: any;
}

const RegisterModal = (props: IProps) => {
    const { trigger, data, onChange, drawerProps } = props;

    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const isEdit = !!data?.id;

    const onOk = async () => {
        const values = await form.validateFields();
        const service = isEdit ? McpApi.updateMcp : McpApi.createMcp;

        const params = {
            tenant: defaultTenant,
            status: IMcpStatusType.active,
            ...(data || {}),
            ...values,
            type: 'internal',
        };

        const res = await service(params);
        if (res.code === 200) {
            message.success(data?.id ? '编辑成功' : '创建成功');
            setOpen(false);
            onChange?.();
        }
    };

    const onOpen = () => {
        if (data) {
            form.setFieldsValue(data);
        } else {
            form.resetFields();
        }
        setOpen(true);
    };

    const onCancel = () => {
        setOpen(false);
    };

    const onToolsAdd = (val: any) => {
        const { tools, ...rest } = form.getFieldValue('meta') || {};
        form.setFieldValue('meta', {
            ...rest,
            tools: (tools || [])?.concat(val)
        })

    };

    const onToolsChange = (val: any) => {
        const { tools, ...rest } = form.getFieldValue('meta') || {};

        form.setFieldValue('meta', {
            ...rest,
            tools: val?.tools
        })
    }

    return <>
        <Flex onClick={onOpen}>
            {trigger || <Button type="primary">创建</Button>}
        </Flex>
        <Drawer
            title="创建"
            open={open}
            width={800}
            onClose={onCancel}
            footer={<Flex justify="end">
                <Space>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type="primary" onClick={onOk}>创建</Button>
                </Space>
            </Flex>}
            {...drawerProps}
        >
            <Form form={form} labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
                <Form.Item label="MCP Code" name="code" rules={[{ required: true }]}>
                    <Input disabled={isEdit} />
                </Form.Item>
                <Form.Item label="通信协议" name="protocol" rules={[{ required: true }]}>
                    <Select disabled={isEdit}
                        options={Object.keys(protocolTypeMap)?.map(item => ({ label: item, value: item }))} />
                </Form.Item>
                <Form.Item label="访问范围" name="accessScope" rules={[{ required: true }]}>
                    <Select disabled={isEdit}
                        options={Object.keys(AccessScopeCnMap).map(scope => ({
                            label: AccessScopeCnMap[scope as keyof typeof AccessScopeCnMap],
                            value: scope
                        }))} />
                </Form.Item>
                <Form.Item label="所属应用" name="appName" >
                    <Input disabled={isEdit} />
                </Form.Item>
                <Form.Item label="sseEndpoint" name="sseEndpoint" rules={[{ required: true }]}>
                    <Input disabled={isEdit} />
                </Form.Item>
                <Form.Item label="messageEndpoint" name="messageEndpoint" rules={[{ required: true }]}>
                    <Input disabled={isEdit} />
                </Form.Item>
                <Form.Item label="工具详情" name="meta" rules={[{ required: true }]}
                    extra={<MetaModal
                        trigger={<Button style={{ width: '100%' }}>添加配置</Button>}
                        onChange={onToolsAdd}
                    />}

                >
                    {/* <Input.TextArea /> */}
                    <MetaToolsPreview
                    // onChange={onToolsChange}
                    />
                </Form.Item>

            </Form>
        </Drawer>
    </>
};

export default RegisterModal;
