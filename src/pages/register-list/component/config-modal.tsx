import { Button, Flex, Form, Modal } from 'antd';
import { useState } from 'react';
import MetaToolsPreview from './meta-tools-preview';

interface IProps {
  data?: any;
  trigger?: React.ReactNode;
  showName?: boolean;
}

const ConfigModal = (props: IProps) => {
  const { data, trigger, showName } = props;
  const [open, setOpen] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    setOpen(false);
  };

  return (
    <>
      <Flex onClick={onOpen}>
        {trigger || <Button type="link">工具详情</Button>}
      </Flex>
      <Modal
        title="工具详情"
        width={800}
        open={open}
        onCancel={onCancel}
        onOk={onOk}
        footer={() => null}
      >
        <Form colon>
          {showName ? (
            <Form.Item label="MCP服务" name="name">
              {data?.name}
            </Form.Item>
          ) : (
            <Form.Item label="MCP服务 Code" name="code">
              {data?.code}
            </Form.Item>
          )}
          {/* <Form.Item label="版本" name="">
                    <Input />
                </Form.Item> */}
          <Form.Item label="">
            <MetaToolsPreview value={data?.meta} isPreview />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ConfigModal;
