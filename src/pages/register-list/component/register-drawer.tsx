import { But<PERSON>, <PERSON>er, <PERSON>lex, Space } from 'antd';
import { useRef, useState } from 'react';
import RegisterForm from '@/pages/create-mcp/component/register-form';
import { inputSchema2Inputs } from '@/utils';

const RegisterDrawer = (props: any) => {
  const { data, onChange, trigger, disabled } = props;
  const [open, setOpen] = useState(false);
  const formRef = useRef(null);

  const onOpen = () => {
    setOpen(true);
    setTimeout(() => {
      const { meta, ...rest } = data;
      // @ts-ignore
      formRef.current?.form?.setFieldsValue({
        ...rest,
        tools: meta?.tools?.map((tool: any) => {
          const { inputSchema, ...others } = tool;
          return {
            ...others,
            inputs: inputSchema2Inputs(inputSchema),
          };
        }),
      });
    }, 0);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onSubmit = () => {
    try {
      // @ts-ignore
      formRef.current?.onSubmit();
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <>
      <Flex onClick={onOpen}>
        {trigger || <Button type="link">编辑</Button>}
      </Flex>
      <Drawer
        title="编辑"
        width={800}
        open={open}
        onClose={onClose}
        footer={
          disabled ? null : (
            <Flex justify="end">
              <Space>
                <Button>取消</Button>
                <Button type="primary" onClick={onSubmit}>
                  提交
                </Button>
              </Space>
            </Flex>
          )
        }
      >
        <RegisterForm
          disabled={disabled}
          ref={formRef}
          type="edit"
          data={data}
          onSuccess={() => {
            setOpen(false);
            onChange?.();
          }}
        />
      </Drawer>
    </>
  );
};

export default RegisterDrawer;
