import { Button, Flex, Form, Input, Modal, message } from 'antd';
import { useState } from 'react';
import ImageUploader from '@/components/ImageUploader';
import StringTags from '@/components/StringTags';
import { defaultMcpCover } from '@/constants';
import { IMcpStatusType } from '@/constants/mcp';
import { McpApi } from '@/services/mcp';

interface IProps {
  data?: any;
  onChange?: () => void;
  trigger?: React.ReactNode;
  modalProps?: any;
}

const PublishModal = (props: IProps) => {
  const { data, onChange, trigger, modalProps } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const onOpen = () => {
    setOpen(true);
    setTimeout(() => {
      form.setFieldsValue(data);
    }, 0);
  };

  const onCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    const res = await McpApi.updateMcp({
      ...(data || {}),
      ...values,
      icon: values?.icon || defaultMcpCover,
      status: IMcpStatusType.published,
    });

    if (res?.code === 200) {
      message.success('发布成功');
      onChange?.();
      onCancel();
    }
  };

  return (
    <>
      <Flex onClick={onOpen}>
        {trigger || <Button type="link">发布</Button>}
      </Flex>

      <Modal
        title="发布"
        width={600}
        open={open}
        onCancel={onCancel}
        onOk={onOk}
        {...modalProps}
      >
        <Form form={form} labelCol={{ span: 5 }}>
          <Form.Item label="MCP名称" name="name" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item label="服务描述" name="description">
            <Input.TextArea />
          </Form.Item>
          <Form.Item label="服务标签" name="tags">
            <StringTags />
          </Form.Item>
          <Form.Item label="服务 Logo Url" name="icon">
            {/* <ImageUploader /> */}
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default PublishModal;
