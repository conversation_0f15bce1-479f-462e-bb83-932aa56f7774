import { useRequest } from '@umijs/max';
import { Modal, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { McpApi } from '@/services/mcp';

interface IProps {
  visible: boolean;
  onCancel: () => void;
  mcp: Record<string, any>;
}

const RegisterInstancesModal = (props: IProps) => {
  const { mcp, visible, onCancel } = props;
  const [data, setData] = useState<any>();

  useEffect(() => {
    if (visible && mcp) {
      run();
    }
  }, [visible]);

  const { loading, run } = useRequest(
    async () => {
      if (!mcp) return { content: [] };
      return await McpApi.queryRegisterInstances({
        env: mcp.env,
        api: mcp.sseEndpoint,
      });
    },
    {
      manual: true,
      onSuccess(data) {
        setData(data);
      },
    },
  );

  const columns = [
    {
      title: '应用',
      dataIndex: 'app',
      key: 'app',
      width: 180,
      render: (text: string) => (
        <Tooltip title={text}>
          <EllipsisText>{text}</EllipsisText>
        </Tooltip>
      ),
    },
    {
      title: '集群',
      dataIndex: 'cluster',
      key: 'cluster',
      width: 180,
      render: (text: string) => (
        <Tooltip title={text}>
          <EllipsisText>{text}</EllipsisText>
        </Tooltip>
      ),
    },
    {
      title: '网关名称',
      dataIndex: 'gatewayName',
      key: 'gatewayName',
      width: 180,
      render: (text: string) => (
        <Tooltip title={text}>
          <EllipsisText>{text}</EllipsisText>
        </Tooltip>
      ),
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 180,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '注册时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 200,
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  return (
    <Modal
      title={`注册实例列表 (${mcp?.code || '未知'})`}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1000}
      destroyOnHidden
    >
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        scroll={{ x: true }}
      />
    </Modal>
  );
};

const EllipsisText = styled.div`
    max-width: 150px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
`;

export default RegisterInstancesModal;
