import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import {
  Button,
  Divider,
  Flex,
  Form,
  Input,
  Modal,
  message,
  Popconfirm,
  Select,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { defaultTenant } from '@/constants';
import {
  IMcpStatusType,
  McpStatusCnMap,
  McpStatusTagColorMap,
  McpTypeCnMap,
} from '@/constants/mcp';
import { McpApi } from '@/services/mcp';
import ConfigModal from './component/config-modal';
import PublishModal from './component/publish-modal';
import RegisterDrawer from './component/register-drawer';
import RegisterInstancesModal from './component/register-instances-modal';

const PAGE_SIZE = 20;

const RegisterList = () => {
  const [pageInfo, setPageInfo] = useState({
    page: 1,
    size: PAGE_SIZE,
  });
  const [data, setData] = useState<any>();
  const [sseEndpointModal, setSseEndpointModal] = useState(false);
  const [selectedMcp, setSelectedMcp] = useState<Record<string, any>>({});
  const [form] = Form.useForm();

  const { run, loading } = useRequest(
    async (params?) => {
      const values = form.getFieldsValue();
      return await McpApi.queryMcps({
        ...pageInfo,
        tenant: defaultTenant,
        ...(values || {}),
        ...(params || {}),
      });
    },
    {
      manual: true,
      onSuccess(data) {
        setData(data);
      },
    },
  );

  useEffect(() => {
    run();
  }, []);

  const onRefresh = () => {
    setPageInfo({
      ...pageInfo,
      page: 1,
    });
    run({
      ...pageInfo,
      page: 1,
    });
  };

  const onRefreshCurrent = () => {
    run();
  };

  const onSearch = async () => {
    const values = form.getFieldsValue();
    setPageInfo({
      ...pageInfo,
      page: 1,
    });
    run({
      ...values,
      ...pageInfo,
      page: 1,
    });
  };

  const onCreate = () => {
    history.push('/service-management/create');
  };

  const onOffLine = async (val: any) => {
    console.log('onOffLine', val);
    const res = await McpApi.updateMcp({
      ...val,
      status: IMcpStatusType.active,
    });
    if (res.code === 200) {
      onRefreshCurrent();
      message.success('下线成功！');
    }
  };

  const handleSseEndpointClick = (record: any) => {
    setSelectedMcp(record);
    setSseEndpointModal(true);
  };

  const handleModalClose = () => {
    setSseEndpointModal(false);
    setSelectedMcp({});
  };

  return (
    <PageContainer>
      <Form layout="inline" form={form}>
        <Form.Item label="服务名称" name="name">
          <Input />
        </Form.Item>
        <Form.Item label="所属应用" name="appName">
          <Input />
        </Form.Item>
        <Form.Item label="状态" name="status">
          <Select
            allowClear
            style={{ width: '100px' }}
            options={Object.keys(McpStatusCnMap)?.map((status) => ({
              label: McpStatusCnMap[status as keyof typeof McpStatusCnMap],
              value: status,
            }))}
          />
        </Form.Item>
        <Button type="primary" onClick={onSearch}>
          查询
        </Button>
      </Form>
      <Flex justify="end" style={{ margin: '10px 0' }}>
        <Button type="primary" onClick={onCreate}>
          创建MCP Server
        </Button>
      </Flex>

      <Table
        loading={loading}
        columns={[
          { title: 'MCP服务CODE', dataIndex: 'code' },
          { title: '所属应用', dataIndex: 'appName' },
          {
            title: 'sseEndpoint',
            dataIndex: 'sseEndpoint',
            render: (sseEndpoint: string, record: any) => (
              <Tooltip title="查看注册实例">
                <Button
                  type="link"
                  onClick={() => handleSseEndpointClick(record)}
                >
                  {sseEndpoint}
                </Button>
              </Tooltip>
            ),
          },
          {
            title: '类型',
            dataIndex: 'type',
            render: (val: keyof typeof McpTypeCnMap) => {
              return <Tag>{McpTypeCnMap[val]}</Tag>;
            },
          },
          { title: '协议类型', dataIndex: 'protocol' },
          {
            title: '工具',
            dataIndex: 'meta',
            render(val, rec) {
              return (
                <ConfigModal
                  data={rec}
                  trigger={
                    <Flex wrap>
                      {val?.tools?.map((tool: any, index: number) => (
                        <Flex align="center" key={tool?.name}>
                          <Tooltip key={tool?.name} title={tool?.description}>
                            <a style={{ cursor: 'pointer' }}>{tool?.name}</a>
                          </Tooltip>
                          {index !== val?.tools?.length - 1 && (
                            <Divider type="vertical" />
                          )}
                        </Flex>
                      ))}
                    </Flex>
                  }
                />
              );
            },
          },
          {
            title: '状态',
            dataIndex: 'status',
            render: (val: string) => {
              return (
                <Tag
                  color={
                    McpStatusTagColorMap[
                      val as keyof typeof McpStatusTagColorMap
                    ]
                  }
                >
                  {McpStatusCnMap[val as keyof typeof McpStatusTagColorMap]}
                </Tag>
              );
            },
          },
          {
            title: '操作',
            dataIndex: 'operate',
            render: (val, rec: any, index) => {
              const isActive = rec?.status === IMcpStatusType.active;
              const isHttp = rec?.protocol === 'http';

              return (
                <Flex>
                  <RegisterDrawer
                    disabled
                    data={rec}
                    onChange={onRefreshCurrent}
                    trigger={<Button type="link">详情</Button>}
                  />

                  {/* 编辑按钮 - 只有已激活且HTTP协议的可以编辑 */}
                  {isActive && isHttp && (
                    <RegisterDrawer
                      disabled={false}
                      data={rec}
                      onChange={onRefreshCurrent}
                      trigger={<Button type="link">编辑</Button>}
                    />
                  )}

                  <PublishModal
                    modalProps={{
                      title:
                        rec?.status === IMcpStatusType.published
                          ? '编辑'
                          : '发布',
                    }}
                    trigger={
                      rec?.status === IMcpStatusType.published ? (
                        <Button type="link">编辑</Button>
                      ) : (
                        <Button type="link">发布</Button>
                      )
                    }
                    data={rec}
                    onChange={onRefreshCurrent}
                  />

                  {rec?.status === IMcpStatusType.published && (
                    <Popconfirm
                      title="确认下线？"
                      onConfirm={() => onOffLine(rec)}
                    >
                      <Button type="link">下线</Button>
                    </Popconfirm>
                  )}
                </Flex>
              );
            },
          },
        ]}
        dataSource={data?.content}
        pagination={{
          current: pageInfo.page,
          total: data?.totalElements,
          showTotal(total, range) {
            return `共 ${total} 条`;
          },
          onChange(page, pageSize) {
            setPageInfo({
              page,
              size: pageSize,
            });
            run({
              page,
              size: pageSize,
            });
          },
        }}
        scroll={{ x: true }}
      />

      <RegisterInstancesModal
        visible={sseEndpointModal}
        mcp={selectedMcp}
        onCancel={handleModalClose}
      />
    </PageContainer>
  );
};

export default RegisterList;
