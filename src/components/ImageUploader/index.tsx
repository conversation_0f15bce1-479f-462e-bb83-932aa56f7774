// @ts-nocheck
// import { getAppId } from '@/utils/state';
import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Modal, Upload } from 'antd';
import type { UploadProps } from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';

type FileType = Parameters<UploadProps['beforeUpload']>[0];

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

export interface UploadImageProps {
  value?: any;
  accept?: string;
  onChange?: (value: any) => void;
  disabled?: boolean;
  type?: 'Image' | 'Audio' | 'Video' | 'ImageNosKey' | 'AudioNosKey' | 'VideoNosKey' | 'Url' | 'NosKey';
  max?: number;
  desc?: string;
  width?: number;
  height?: number;
  uploadProps?: any;
  permanent?: boolean;
  sizeLimit?: number; // 大小限制 单位：KB
  beforeUpload?: (value: any) => Promise<boolean>;
}

interface CustomUploadWrapperProps {
  width?: number;
  height?: number;
}

const CustomUploadWrapper = styled.div<CustomUploadWrapperProps>`
.ant-upload-wrapper {
  height: ${props => props.height}px; /* 设置图片卡片的高度 */
  padding: ${props => props.height ? 0 : 10}px!important;

  .ant-upload {
    width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
    height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
  }
}
.ant-upload-list-picture-card .ant-upload-list-item {
  width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
  height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
}
.ant-upload-list-item-container {
  width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
  height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
}

.ant-upload-list-picture-card .ant-upload-list-item-thumbnail {
  // width: 100%; /* 确保图片占满卡片 */
  // height: 100%; /* 确保图片占满卡片 */
  object-fit: cover; /* 保持图片的纵横比 */
}
`;

const acceptMap = {
  'Image': 'image/*',
  'Audio': 'audio/*',
  'Video': 'video/*',
  'ImageNosKey': 'image/*',
  'AudioNosKey': 'audio/*',
  'VideoNosKey': 'video/*',
}

const ImageUploader: React.FC<UploadImageProps> = ({ value, onChange, disabled, type = 'Image', max = 1, desc = 'Upload', width = 100, height = 100, uploadProps = { showUploadList: true }, accept, permanent, sizeLimit, beforeUpload: parentBeforeUpload }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState(() => {
    console.log('value...', value);
    if (value && typeof value === 'string') {
      return [{
        url: value,
        status: 'done'
      }];
    }
    if (value && value.status !== 'removed') {
      return [value];
    }
    return [];
  });
  const handleCancel = () => setPreviewOpen(false);
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.uri.substring(file.url.lastIndexOf('/') + 1));
  };

  const formatFile = (file) => {
    let obj: any = file;
    if (file?.response?.data) {
      obj = {
        key: file?.response?.data?.key,
        url: file?.response?.data?.uri,
        name: obj.name,
        status: obj.status,
        file: file.originFileObj
      };
      // if (type === 'Image' || type === 'Audio' || type === 'Video') {
      //   obj = file?.response?.data?.uri;
      // }
      // if (type === 'ImageNosKey' || type === 'AudioNosKey' || type === 'VideoNosKey') {
      //   obj = file?.response?.data?.key;
      // }
    }
    return obj;
  };

  const handleChange = (val, obj) => {
    const { fileList: newFileList } = val;
    // 说明是删除
    if (!newFileList.length) {
      setFileList([]);
      onChange(null);
      return;
    }

    if (max === 1) {
      if (newFileList[0].status === 'done') {

        if (onChange) {
          onChange(newFileList.map(formatFile)[0]);
        }
      }
    } else if (max > 1) {
      onChange(newFileList.map(formatFile))
    }
    setFileList(newFileList);
  };

  const uploadButton = () => {
    if (!uploadProps?.showUploadList && value?.url) {

      return <img width={width} height={height} src={value?.url} style={{
        borderRadius: 4
      }} />
    }

    if (width <= 50 || height <= 50) {
      return <div style={{ height, width, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <PlusOutlined />
      </div>
    }

    return <div >
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
        }}
      >
        {desc}
      </div>
    </div>
  }
  const appId = 'langbase-mcp';
  // const actionUrl = env === 'online' ? `/api/v1/app/${getAppId()}/uploadfile` : `http://langbase-workflow.yf-onlinetest4.netease.com/api/v1/app/${getAppId()}/uploadfile`;
//   const actionUrl = `/api/v1/app/${getAppId()}/uploadfile?${permanent ? 'permanent=true' : ''}`
  const actionUrl = `/api/v1/app/${appId}/uploadfile?${permanent ? 'permanent=true' : ''}`


  const beforeUpload = async (file: FileType) => {
    if (parentBeforeUpload) {
      try {
        const before = await parentBeforeUpload(file);
        if (!before) return false;
      } catch (error) {
        console.log('beforeUpload.error', error);
        return false
      }
    }

    if (!sizeLimit) {
      return true;
    }
    const isLimited = file.size < sizeLimit * 1024;
    if (!isLimited) {
      const unit = sizeLimit >= 1024 ? 'MB' : 'KB';
      const size = sizeLimit >= 1024
        ? (sizeLimit / 1024).toFixed(0)
        : (sizeLimit).toFixed(0);

      const fileSize = file.size / 1024; // 转换为 KB
      const fileSizeStr = fileSize >= 1024
        ? `${(fileSize / 1024).toFixed(2)}MB`
        : `${fileSize.toFixed(2)}KB`;

      message.error(`文件大小超出限制：当前大小 ${fileSizeStr}，限制大小 ${size}${unit}!`);
      return isLimited;
    }
    return isLimited;
  };

  const uploader = <Upload
    action={actionUrl}
    listType="picture-card"
    disabled={disabled}
    fileList={fileList}
    accept={accept || acceptMap[type]}
    onPreview={handlePreview}
    onChange={handleChange}
    beforeUpload={beforeUpload}
    // beforeUpload={file=>{
    //   return new Promise((resolve, reject) => {
    //     const reader = new FileReader();
    //     reader.readAsDataURL(file);
    //     reader.onload = (e) => {
    //       const img = new Image();
    //       img.src = e.target.result;
    //       img.onload = () => {
    //         const { width, height } = img;
    //         console.log('Image width:', width);
    //         console.log('Image height:', height);
    //         file.width = width;
    //         file.height = height;
    //         resolve(file); // 继续上传
    //       };
    //       img.onerror = () => {
    //         message.error('图片加载失败');
    //         reject('图片加载失败');
    //       };
    //     };
    //     reader.onerror = () => {
    //       message.error('文件读取失败');
    //       reject('文件读取失败');
    //     };
    //   });
    // }}
    {...uploadProps}
  >
    {fileList.length >= max && uploadProps?.showUploadList ? null : uploadButton()}
  </Upload>

  const modal = <Modal
    open={previewOpen}
    title={previewTitle}
    width={900}
    footer={null}
    onCancel={handleCancel}
  >
    {(type === 'Image' || type === 'ImageNosKey') && (
      <img
        alt="example"

        style={{
          width: '100%',

        }}
        src={previewImage}
      />
    )}
    {(type === 'Audio' || type === 'AudioNosKey') && (
      <audio controls src={previewImage}></audio>
    )}
    {(type === 'Video' || type === 'VideoNosKey') && (
      <video controls src={previewImage} style={{ width: '100%' }}></video>
    )}
    {(type === 'Url' || type === 'NosKey') && (
      <Button type="primary" onClick={() => {
        window.open(previewImage, '_blank');
      }}>
        打开链接
      </Button>

    )}
  </Modal>

  return (
    <CustomUploadWrapper width={width} height={height}>
      {uploader}
      {modal}
    </CustomUploadWrapper>
  );
};
export default ImageUploader;
